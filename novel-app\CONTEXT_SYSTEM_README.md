# 小说上下文记忆系统

## 概述

这个上下文记忆系统解决了小说重写过程中每章单独处理导致的上下文丢失问题。通过AI预分析和上下文注入，确保重写内容的连贯性和一致性。

## 核心功能

### 1. 小说整体上下文分析
- 提取小说摘要
- 识别主要人物和关系
- 分析世界观设定
- 识别写作风格和语调
- 提取主要情节线和主题

### 2. 章节上下文分析
- 识别章节关键事件
- 跟踪人物状态变化
- 分析情节推进要点
- 建立章节间的关联

### 3. 上下文注入重写
- 在重写时自动注入相关上下文
- 保持人物性格一致性
- 维护情节逻辑连贯性
- 保持写作风格统一

## 使用流程

### 1. 上传和解析小说
```bash
POST /api/novels/parse
{
  "filePath": "path/to/novel.txt"
}
```

### 2. 分析小说上下文
```bash
POST /api/context/analyze
{
  "novelId": "novel-id",
  "analyzeChapters": true
}
```

### 3. 查看上下文信息
```bash
# 获取小说整体上下文
GET /api/context/novel?novelId=novel-id

# 获取章节上下文
GET /api/context/chapter?novelId=novel-id&chapterNumber=1

# 获取章节上下文窗口
GET /api/context/window?novelId=novel-id&chapterNumber=2&windowSize=2
```

### 4. 使用上下文进行重写
重写API会自动使用上下文信息，无需额外配置。

## API 端点

### 上下文分析
- `POST /api/context/analyze` - 分析小说并生成上下文
- `GET /api/context/novel` - 获取小说上下文
- `PUT /api/context/novel` - 更新小说上下文
- `DELETE /api/context/novel` - 删除小说上下文

### 章节上下文
- `GET /api/context/chapter` - 获取章节上下文
- `PUT /api/context/chapter` - 更新章节上下文
- `DELETE /api/context/chapter` - 删除章节上下文
- `GET /api/context/window` - 获取章节上下文窗口

## 数据结构

### NovelContext（小说上下文）
```typescript
{
  id: string;
  novelId: string;
  summary: string;                    // 小说摘要
  mainCharacters: Array<{             // 主要人物
    name: string;
    role: string;
    description: string;
    relationships?: string;
  }>;
  worldSetting: string;               // 世界观设定
  writingStyle: string;               // 写作风格
  mainPlotlines: string[];            // 主要情节线
  themes: string[];                   // 主题
  tone: string;                       // 语调
  createdAt: string;
  updatedAt: string;
}
```

### ChapterContext（章节上下文）
```typescript
{
  id: string;
  novelId: string;
  chapterNumber: number;
  keyEvents: string[];                // 关键事件
  characterStates: Array<{            // 人物状态
    name: string;
    status: string;
    emotions: string;
    relationships: string;
  }>;
  plotProgress: string;               // 情节推进
  previousChapterSummary?: string;    // 前章摘要
  nextChapterHints?: string;          // 下章暗示
  contextualNotes: string;            // 上下文注释
  createdAt: string;
  updatedAt: string;
}
```

## 优势

1. **连贯性保证**：确保重写内容与整体背景一致
2. **人物一致性**：维护人物性格和关系的连贯性
3. **情节逻辑**：保持故事发展的逻辑性
4. **风格统一**：维持原有的写作风格和语调
5. **智能优化**：根据上下文智能调整重写策略

## 性能考虑

- 上下文分析是一次性操作，后续重写直接使用
- 支持增量更新，只分析新增或修改的章节
- 上下文窗口机制避免过多信息干扰
- 自动回退机制，确保系统稳定性

## 测试

运行测试脚本验证系统功能：
```bash
node test-context-system.js
```

## 注意事项

1. 首次使用需要先进行上下文分析
2. 分析过程可能需要较长时间，请耐心等待
3. 建议在重写前检查上下文信息的准确性
4. 可以手动编辑上下文信息以获得更好的重写效果
