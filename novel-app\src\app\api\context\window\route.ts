import { NextRequest, NextResponse } from 'next/server';
import { chapterContextDb } from '@/lib/database';

// GET - 获取章节上下文窗口（前后几章的上下文）
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const novelId = searchParams.get('novelId');
    const chapterNumber = searchParams.get('chapterNumber');
    const windowSize = searchParams.get('windowSize');
    
    if (!novelId || !chapterNumber) {
      return NextResponse.json(
        { success: false, error: '小说ID和章节号不能为空' },
        { status: 400 }
      );
    }
    
    const chapterNum = parseInt(chapterNumber);
    const windowSizeNum = windowSize ? parseInt(windowSize) : 2;
    
    if (isNaN(chapterNum) || isNaN(windowSizeNum)) {
      return NextResponse.json(
        { success: false, error: '章节号和窗口大小必须是有效数字' },
        { status: 400 }
      );
    }
    
    const contextWindow = chapterContextDb.getContextWindow(novelId, chapterNum, windowSizeNum);
    
    return NextResponse.json({
      success: true,
      data: {
        targetChapter: chapterNum,
        windowSize: windowSizeNum,
        contexts: contextWindow,
        totalContexts: contextWindow.length
      }
    });
  } catch (error) {
    console.error('获取章节上下文窗口失败:', error);
    return NextResponse.json(
      { success: false, error: '获取章节上下文窗口失败' },
      { status: 500 }
    );
  }
}
