(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/src/lib/database.ts [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_next_dist_compiled_crypto-browserify_index_44cf344e.js",
  "static/chunks/node_modules_next_dist_compiled_38c90ac5._.js",
  "static/chunks/src_lib_database_ts_a2e80df2._.js",
  "static/chunks/src_lib_database_ts_012c5484._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/database.ts [app-client] (ecmascript)");
    });
});
}),
]);