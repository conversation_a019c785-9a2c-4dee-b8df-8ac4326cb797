import fs from 'fs';
import path from 'path';
import crypto from 'crypto';

// 数据类型定义
export interface Novel {
  id: string;
  title: string;
  filename: string;
  createdAt: string;
  chapterCount?: number;
}

export interface Chapter {
  id: string;
  novelId: string;
  chapterNumber: number;
  title: string;
  content: string;
  filename: string;
  createdAt: string;
}

export interface RewriteRule {
  id: string;
  name: string;
  description: string;
  rules: string;
  createdAt: string;
  updatedAt: string;
}

export interface Character {
  id: string;
  novelId: string;
  name: string;
  role: string; // 角色类型：男主、女主、配角、反派、其他
  description: string;
  personality?: string; // 性格特点
  appearance?: string; // 外貌描述
  relationships?: string; // 人物关系
  createdAt: string;
  updatedAt: string;
}

// 小说整体上下文
export interface NovelContext {
  id: string;
  novelId: string;
  summary: string; // 小说整体摘要
  mainCharacters: Array<{
    name: string;
    role: string;
    description: string;
    relationships?: string;
  }>; // 主要人物信息
  worldSetting: string; // 世界观设定
  writingStyle: string; // 写作风格特征
  mainPlotlines: string[]; // 主要情节线
  themes: string[]; // 主题
  tone: string; // 语调风格
  createdAt: string;
  updatedAt: string;
}

// 章节上下文
export interface ChapterContext {
  id: string;
  novelId: string;
  chapterNumber: number;
  keyEvents: string[]; // 关键事件
  characterStates: Array<{
    name: string;
    status: string; // 人物在本章的状态
    emotions: string; // 情感状态
    relationships: string; // 关系变化
  }>; // 人物状态
  plotProgress: string; // 情节推进要点
  previousChapterSummary?: string; // 前一章摘要
  nextChapterHints?: string; // 对下一章的暗示
  contextualNotes: string; // 上下文注释
  createdAt: string;
  updatedAt: string;
}

export interface Preset {
  id: string;
  name: string;
  description: string;
  rules: string;
  createdAt: string;
  updatedAt: string;
}

export interface RewriteJob {
  id: string;
  novelId: string;
  chapters: number[];
  ruleId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  result?: string;
  createdAt: string;
  updatedAt: string;
  // 新增详细信息字段
  details?: {
    totalChapters: number;
    completedChapters: number;
    failedChapters: number;
    totalTokensUsed: number;
    totalProcessingTime: number;
    averageTimePerChapter: number;
    apiKeyStats: Array<{
      name: string;
      requestCount: number;
      weight: number;
      isAvailable: boolean;
    }>;
    chapterResults: Array<{
      chapterNumber: number;
      chapterTitle: string;
      success: boolean;
      error?: string;
      apiKeyUsed?: string;
      tokensUsed?: number;
      processingTime?: number;
      completedAt?: string;
    }>;
    model?: string;
    concurrency?: number;
  };
}

// 数据存储路径
const DATA_DIR = path.join(process.cwd(), 'data');
const NOVELS_FILE = path.join(DATA_DIR, 'novels.json');
const CHAPTERS_FILE = path.join(DATA_DIR, 'chapters.json');
const RULES_FILE = path.join(DATA_DIR, 'rewrite_rules.json');
const JOBS_FILE = path.join(DATA_DIR, 'rewrite_jobs.json');
const CHARACTERS_FILE = path.join(DATA_DIR, 'characters.json');
const PRESETS_FILE = path.join(DATA_DIR, 'presets.json');
const NOVEL_CONTEXTS_FILE = path.join(DATA_DIR, 'novel-contexts.json');
const CHAPTER_CONTEXTS_FILE = path.join(DATA_DIR, 'chapter-contexts.json');

// 确保数据目录存在
function ensureDataDir() {
  if (!fs.existsSync(DATA_DIR)) {
    fs.mkdirSync(DATA_DIR, { recursive: true });
  }
}

// 读取JSON文件
function readJsonFile<T>(filePath: string): T[] {
  ensureDataDir();
  if (!fs.existsSync(filePath)) {
    return [];
  }
  try {
    const data = fs.readFileSync(filePath, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading ${filePath}:`, error);
    return [];
  }
}

// 写入JSON文件
function writeJsonFile<T>(filePath: string, data: T[]) {
  ensureDataDir();
  try {
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf-8');
  } catch (error) {
    console.error(`Error writing ${filePath}:`, error);
    throw error;
  }
}

// 生成唯一ID
function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
}

// 基于内容生成确定性ID
function generateDeterministicId(content: string): string {
  return crypto.createHash('md5').update(content).digest('hex').substring(0, 18);
}

// 小说相关操作
export const novelDb = {
  getAll: (): Novel[] => readJsonFile<Novel>(NOVELS_FILE),

  getById: (id: string): Novel | undefined => {
    const novels = readJsonFile<Novel>(NOVELS_FILE);
    return novels.find(novel => novel.id === id);
  },

  create: (novel: Omit<Novel, 'id' | 'createdAt'>): Novel => {
    const novels = readJsonFile<Novel>(NOVELS_FILE);

    // 使用书名生成确定性ID
    const novelId = generateDeterministicId(novel.title);

    // 检查是否已存在相同ID的小说
    const existingNovel = novels.find(n => n.id === novelId);
    if (existingNovel) {
      // 如果已存在，更新现有记录
      existingNovel.filename = novel.filename;
      existingNovel.chapterCount = novel.chapterCount;
      writeJsonFile(NOVELS_FILE, novels);
      return existingNovel;
    }

    const newNovel: Novel = {
      ...novel,
      id: novelId,
      createdAt: new Date().toISOString(),
    };
    novels.push(newNovel);
    writeJsonFile(NOVELS_FILE, novels);
    return newNovel;
  },

  update: (id: string, updates: Partial<Novel>): Novel | null => {
    const novels = readJsonFile<Novel>(NOVELS_FILE);
    const index = novels.findIndex(novel => novel.id === id);
    if (index === -1) return null;

    novels[index] = { ...novels[index], ...updates };
    writeJsonFile(NOVELS_FILE, novels);
    return novels[index];
  },

  delete: (id: string): boolean => {
    const novels = readJsonFile<Novel>(NOVELS_FILE);
    const index = novels.findIndex(novel => novel.id === id);
    if (index === -1) return false;

    novels.splice(index, 1);
    writeJsonFile(NOVELS_FILE, novels);
    return true;
  }
};

// 章节相关操作
export const chapterDb = {
  getAll: (): Chapter[] => readJsonFile<Chapter>(CHAPTERS_FILE),

  getByNovelId: (novelId: string): Chapter[] => {
    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);
    return chapters.filter(chapter => chapter.novelId === novelId);
  },

  getById: (id: string): Chapter | undefined => {
    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);
    return chapters.find(chapter => chapter.id === id);
  },

  create: (chapter: Omit<Chapter, 'id' | 'createdAt'>): Chapter => {
    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);
    const newChapter: Chapter = {
      ...chapter,
      id: generateId(),
      createdAt: new Date().toISOString(),
    };
    chapters.push(newChapter);
    writeJsonFile(CHAPTERS_FILE, chapters);
    return newChapter;
  },

  createBatch: (chapters: Omit<Chapter, 'id' | 'createdAt'>[]): Chapter[] => {
    const existingChapters = readJsonFile<Chapter>(CHAPTERS_FILE);
    const newChapters = chapters.map(chapter => ({
      ...chapter,
      id: generateId(),
      createdAt: new Date().toISOString(),
    }));
    existingChapters.push(...newChapters);
    writeJsonFile(CHAPTERS_FILE, existingChapters);
    return newChapters;
  },

  delete: (id: string): boolean => {
    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);
    const index = chapters.findIndex(chapter => chapter.id === id);
    if (index === -1) return false;

    chapters.splice(index, 1);
    writeJsonFile(CHAPTERS_FILE, chapters);
    return true;
  },

  deleteByNovelId: (novelId: string): boolean => {
    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);
    const filteredChapters = chapters.filter(chapter => chapter.novelId !== novelId);
    writeJsonFile(CHAPTERS_FILE, filteredChapters);
    return true;
  }
};

// 改写规则相关操作
export const ruleDb = {
  getAll: (): RewriteRule[] => readJsonFile<RewriteRule>(RULES_FILE),

  getById: (id: string): RewriteRule | undefined => {
    const rules = readJsonFile<RewriteRule>(RULES_FILE);
    return rules.find(rule => rule.id === id);
  },

  create: (rule: Omit<RewriteRule, 'id' | 'createdAt' | 'updatedAt'>): RewriteRule => {
    const rules = readJsonFile<RewriteRule>(RULES_FILE);
    const newRule: RewriteRule = {
      ...rule,
      id: generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    rules.push(newRule);
    writeJsonFile(RULES_FILE, rules);
    return newRule;
  },

  update: (id: string, updates: Partial<RewriteRule>): RewriteRule | null => {
    const rules = readJsonFile<RewriteRule>(RULES_FILE);
    const index = rules.findIndex(rule => rule.id === id);
    if (index === -1) return null;

    rules[index] = {
      ...rules[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };
    writeJsonFile(RULES_FILE, rules);
    return rules[index];
  },

  delete: (id: string): boolean => {
    const rules = readJsonFile<RewriteRule>(RULES_FILE);
    const index = rules.findIndex(rule => rule.id === id);
    if (index === -1) return false;

    rules.splice(index, 1);
    writeJsonFile(RULES_FILE, rules);
    return true;
  }
};

// 改写任务相关操作
export const jobDb = {
  getAll: (): RewriteJob[] => readJsonFile<RewriteJob>(JOBS_FILE),

  getById: (id: string): RewriteJob | undefined => {
    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);
    return jobs.find(job => job.id === id);
  },

  create: (job: Omit<RewriteJob, 'id' | 'createdAt' | 'updatedAt'>): RewriteJob => {
    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);
    const newJob: RewriteJob = {
      ...job,
      id: generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    jobs.push(newJob);
    writeJsonFile(JOBS_FILE, jobs);
    return newJob;
  },

  update: (id: string, updates: Partial<RewriteJob>): RewriteJob | null => {
    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);
    const index = jobs.findIndex(job => job.id === id);
    if (index === -1) return null;

    jobs[index] = {
      ...jobs[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };
    writeJsonFile(JOBS_FILE, jobs);
    return jobs[index];
  },

  delete: (id: string): boolean => {
    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);
    const index = jobs.findIndex(job => job.id === id);
    if (index === -1) return false;

    jobs.splice(index, 1);
    writeJsonFile(JOBS_FILE, jobs);
    return true;
  }
};

// 人物设定相关操作
export const characterDb = {
  getAll: (): Character[] => readJsonFile<Character>(CHARACTERS_FILE),

  getByNovelId: (novelId: string): Character[] => {
    const characters = readJsonFile<Character>(CHARACTERS_FILE);
    return characters.filter(character => character.novelId === novelId);
  },

  getById: (id: string): Character | undefined => {
    const characters = readJsonFile<Character>(CHARACTERS_FILE);
    return characters.find(character => character.id === id);
  },

  create: (character: Omit<Character, 'id' | 'createdAt' | 'updatedAt'>): Character => {
    const characters = readJsonFile<Character>(CHARACTERS_FILE);
    const newCharacter: Character = {
      ...character,
      id: generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    characters.push(newCharacter);
    writeJsonFile(CHARACTERS_FILE, characters);
    return newCharacter;
  },

  update: (id: string, updates: Partial<Character>): Character | null => {
    const characters = readJsonFile<Character>(CHARACTERS_FILE);
    const index = characters.findIndex(character => character.id === id);
    if (index === -1) return null;

    characters[index] = {
      ...characters[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };
    writeJsonFile(CHARACTERS_FILE, characters);
    return characters[index];
  },

  delete: (id: string): boolean => {
    const characters = readJsonFile<Character>(CHARACTERS_FILE);
    const index = characters.findIndex(character => character.id === id);
    if (index === -1) return false;

    characters.splice(index, 1);
    writeJsonFile(CHARACTERS_FILE, characters);
    return true;
  },

  deleteByNovelId: (novelId: string): boolean => {
    const characters = readJsonFile<Character>(CHARACTERS_FILE);
    const filteredCharacters = characters.filter(character => character.novelId !== novelId);
    writeJsonFile(CHARACTERS_FILE, filteredCharacters);
    return true;
  }
};

// 预设相关操作
export const presetDb = {
  getAll: (): Preset[] => readJsonFile<Preset>(PRESETS_FILE),

  getById: (id: string): Preset | undefined => {
    const presets = readJsonFile<Preset>(PRESETS_FILE);
    return presets.find(preset => preset.id === id);
  },

  create: (preset: Omit<Preset, 'id' | 'createdAt' | 'updatedAt'>): Preset => {
    const presets = readJsonFile<Preset>(PRESETS_FILE);
    const newPreset: Preset = {
      ...preset,
      id: generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    presets.push(newPreset);
    writeJsonFile(PRESETS_FILE, presets);
    return newPreset;
  },

  update: (id: string, updates: Partial<Preset>): Preset | null => {
    const presets = readJsonFile<Preset>(PRESETS_FILE);
    const index = presets.findIndex(preset => preset.id === id);
    if (index === -1) return null;

    presets[index] = {
      ...presets[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };
    writeJsonFile(PRESETS_FILE, presets);
    return presets[index];
  },

  delete: (id: string): boolean => {
    const presets = readJsonFile<Preset>(PRESETS_FILE);
    const index = presets.findIndex(preset => preset.id === id);
    if (index === -1) return false;

    presets.splice(index, 1);
    writeJsonFile(PRESETS_FILE, presets);
    return true;
  }
};

// 小说上下文相关操作
export const novelContextDb = {
  getAll: (): NovelContext[] => readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE),

  getByNovelId: (novelId: string): NovelContext | undefined => {
    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);
    return contexts.find(context => context.novelId === novelId);
  },

  getById: (id: string): NovelContext | undefined => {
    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);
    return contexts.find(context => context.id === id);
  },

  create: (context: Omit<NovelContext, 'id' | 'createdAt' | 'updatedAt'>): NovelContext => {
    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);
    const newContext: NovelContext = {
      ...context,
      id: generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    contexts.push(newContext);
    writeJsonFile(NOVEL_CONTEXTS_FILE, contexts);
    return newContext;
  },

  update: (id: string, updates: Partial<Omit<NovelContext, 'id' | 'createdAt'>>): NovelContext | undefined => {
    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);
    const index = contexts.findIndex(context => context.id === id);
    if (index === -1) return undefined;

    contexts[index] = {
      ...contexts[index],
      ...updates,
      updatedAt: new Date().toISOString(),
    };
    writeJsonFile(NOVEL_CONTEXTS_FILE, contexts);
    return contexts[index];
  },

  delete: (id: string): boolean => {
    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);
    const index = contexts.findIndex(context => context.id === id);
    if (index === -1) return false;

    contexts.splice(index, 1);
    writeJsonFile(NOVEL_CONTEXTS_FILE, contexts);
    return true;
  }
};

// 章节上下文相关操作
export const chapterContextDb = {
  getAll: (): ChapterContext[] => readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE),

  getByNovelId: (novelId: string): ChapterContext[] => {
    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);
    return contexts.filter(context => context.novelId === novelId);
  },

  getByChapter: (novelId: string, chapterNumber: number): ChapterContext | undefined => {
    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);
    return contexts.find(context =>
      context.novelId === novelId && context.chapterNumber === chapterNumber
    );
  },

  getById: (id: string): ChapterContext | undefined => {
    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);
    return contexts.find(context => context.id === id);
  },

  create: (context: Omit<ChapterContext, 'id' | 'createdAt' | 'updatedAt'>): ChapterContext => {
    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);
    const newContext: ChapterContext = {
      ...context,
      id: generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    contexts.push(newContext);
    writeJsonFile(CHAPTER_CONTEXTS_FILE, contexts);
    return newContext;
  },

  update: (id: string, updates: Partial<Omit<ChapterContext, 'id' | 'createdAt'>>): ChapterContext | undefined => {
    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);
    const index = contexts.findIndex(context => context.id === id);
    if (index === -1) return undefined;

    contexts[index] = {
      ...contexts[index],
      ...updates,
      updatedAt: new Date().toISOString(),
    };
    writeJsonFile(CHAPTER_CONTEXTS_FILE, contexts);
    return contexts[index];
  },

  delete: (id: string): boolean => {
    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);
    const index = contexts.findIndex(context => context.id === id);
    if (index === -1) return false;

    contexts.splice(index, 1);
    writeJsonFile(CHAPTER_CONTEXTS_FILE, contexts);
    return true;
  },

  // 获取章节的上下文窗口（前后几章的上下文）
  getContextWindow: (novelId: string, chapterNumber: number, windowSize: number = 2): ChapterContext[] => {
    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);
    const novelContexts = contexts.filter(context => context.novelId === novelId);

    const startChapter = Math.max(1, chapterNumber - windowSize);
    const endChapter = chapterNumber + windowSize;

    return novelContexts.filter(context =>
      context.chapterNumber >= startChapter && context.chapterNumber <= endChapter
    ).sort((a, b) => a.chapterNumber - b.chapterNumber);
  }
};
