import { NextRequest, NextResponse } from 'next/server';
import { novelContextDb } from '@/lib/database';

// GET - 获取小说上下文
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const novelId = searchParams.get('novelId');
    
    if (!novelId) {
      return NextResponse.json(
        { success: false, error: '小说ID不能为空' },
        { status: 400 }
      );
    }
    
    const context = novelContextDb.getByNovelId(novelId);
    
    if (!context) {
      return NextResponse.json(
        { success: false, error: '未找到小说上下文，请先进行分析' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: context,
    });
  } catch (error) {
    console.error('获取小说上下文失败:', error);
    return NextResponse.json(
      { success: false, error: '获取小说上下文失败' },
      { status: 500 }
    );
  }
}

// PUT - 更新小说上下文
export async function PUT(request: NextRequest) {
  try {
    const { novelId, ...updates } = await request.json();
    
    if (!novelId) {
      return NextResponse.json(
        { success: false, error: '小说ID不能为空' },
        { status: 400 }
      );
    }
    
    const existingContext = novelContextDb.getByNovelId(novelId);
    if (!existingContext) {
      return NextResponse.json(
        { success: false, error: '未找到小说上下文' },
        { status: 404 }
      );
    }
    
    const updatedContext = novelContextDb.update(existingContext.id, updates);
    
    if (!updatedContext) {
      return NextResponse.json(
        { success: false, error: '更新失败' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: updatedContext,
      message: '小说上下文更新成功'
    });
  } catch (error) {
    console.error('更新小说上下文失败:', error);
    return NextResponse.json(
      { success: false, error: '更新小说上下文失败' },
      { status: 500 }
    );
  }
}

// DELETE - 删除小说上下文
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const novelId = searchParams.get('novelId');
    
    if (!novelId) {
      return NextResponse.json(
        { success: false, error: '小说ID不能为空' },
        { status: 400 }
      );
    }
    
    const existingContext = novelContextDb.getByNovelId(novelId);
    if (!existingContext) {
      return NextResponse.json(
        { success: false, error: '未找到小说上下文' },
        { status: 404 }
      );
    }
    
    const deleted = novelContextDb.delete(existingContext.id);
    
    if (!deleted) {
      return NextResponse.json(
        { success: false, error: '删除失败' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      success: true,
      message: '小说上下文删除成功'
    });
  } catch (error) {
    console.error('删除小说上下文失败:', error);
    return NextResponse.json(
      { success: false, error: '删除小说上下文失败' },
      { status: 500 }
    );
  }
}
