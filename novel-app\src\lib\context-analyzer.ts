import { rewriteText } from './gemini';
import { novelContextDb, chapterContextDb, chapterDb, novelDb } from './database';
import type { NovelContext, ChapterContext, Chapter } from './database';

// 上下文分析器
export class ContextAnalyzer {
  
  // 分析整个小说并生成上下文
  async analyzeNovel(novelId: string): Promise<NovelContext> {
    const novel = novelDb.getById(novelId);
    if (!novel) {
      throw new Error('小说不存在');
    }

    const chapters = chapterDb.getByNovelId(novelId);
    if (chapters.length === 0) {
      throw new Error('小说没有章节');
    }

    // 合并所有章节内容用于整体分析
    const fullText = chapters
      .sort((a, b) => a.chapterNumber - b.chapterNumber)
      .map(chapter => `第${chapter.chapterNumber}章 ${chapter.title}\n\n${chapter.content}`)
      .join('\n\n---\n\n');

    // 构建分析提示词
    const analysisPrompt = `
请分析以下小说的整体内容，提取关键信息：

小说标题：${novel.title}

小说内容：
${fullText}

请按照以下JSON格式返回分析结果：
{
  "summary": "小说整体摘要（200-300字）",
  "mainCharacters": [
    {
      "name": "人物姓名",
      "role": "角色类型（男主/女主/配角/反派等）",
      "description": "人物描述",
      "relationships": "人物关系"
    }
  ],
  "worldSetting": "世界观设定描述",
  "writingStyle": "写作风格特征描述",
  "mainPlotlines": ["主要情节线1", "主要情节线2"],
  "themes": ["主题1", "主题2"],
  "tone": "整体语调风格"
}

请确保返回的是有效的JSON格式。
`;

    try {
      const result = await rewriteText({
        originalText: analysisPrompt,
        rules: '请严格按照要求的JSON格式返回分析结果，不要添加任何其他内容。',
        chapterTitle: '小说整体分析',
        chapterNumber: 0,
        model: 'gemini-2.5-flash-lite'
      });

      if (!result.success) {
        throw new Error(`分析失败: ${result.error}`);
      }

      // 解析AI返回的JSON
      let analysisData;
      try {
        // 尝试提取JSON部分
        const jsonMatch = result.rewrittenText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          analysisData = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error('无法找到JSON格式的分析结果');
        }
      } catch (parseError) {
        console.error('解析分析结果失败:', parseError);
        // 如果解析失败，使用默认结构
        analysisData = {
          summary: '分析结果解析失败，请重新分析',
          mainCharacters: [],
          worldSetting: '未知',
          writingStyle: '未知',
          mainPlotlines: [],
          themes: [],
          tone: '未知'
        };
      }

      // 检查是否已存在上下文
      const existingContext = novelContextDb.getByNovelId(novelId);
      
      if (existingContext) {
        // 更新现有上下文
        return novelContextDb.update(existingContext.id, {
          summary: analysisData.summary,
          mainCharacters: analysisData.mainCharacters,
          worldSetting: analysisData.worldSetting,
          writingStyle: analysisData.writingStyle,
          mainPlotlines: analysisData.mainPlotlines,
          themes: analysisData.themes,
          tone: analysisData.tone
        })!;
      } else {
        // 创建新的上下文
        return novelContextDb.create({
          novelId,
          summary: analysisData.summary,
          mainCharacters: analysisData.mainCharacters,
          worldSetting: analysisData.worldSetting,
          writingStyle: analysisData.writingStyle,
          mainPlotlines: analysisData.mainPlotlines,
          themes: analysisData.themes,
          tone: analysisData.tone
        });
      }

    } catch (error) {
      console.error('小说分析失败:', error);
      throw error;
    }
  }

  // 分析单个章节并生成章节上下文
  async analyzeChapter(novelId: string, chapterNumber: number): Promise<ChapterContext> {
    const chapter = chapterDb.getByNovelId(novelId).find(c => c.chapterNumber === chapterNumber);
    if (!chapter) {
      throw new Error(`第${chapterNumber}章不存在`);
    }

    const novelContext = novelContextDb.getByNovelId(novelId);
    if (!novelContext) {
      throw new Error('请先分析小说整体上下文');
    }

    // 获取前一章的上下文（如果存在）
    const previousChapterContext = chapterContextDb.getByChapter(novelId, chapterNumber - 1);
    
    // 构建章节分析提示词
    const analysisPrompt = `
请分析以下章节的内容，提取关键信息：

小说背景：
${novelContext.summary}

主要人物：
${novelContext.mainCharacters.map(char => `${char.name}(${char.role}): ${char.description}`).join('\n')}

${previousChapterContext ? `前一章摘要：\n${previousChapterContext.previousChapterSummary || '无'}` : ''}

当前章节：
第${chapter.chapterNumber}章 ${chapter.title}

${chapter.content}

请按照以下JSON格式返回分析结果：
{
  "keyEvents": ["关键事件1", "关键事件2"],
  "characterStates": [
    {
      "name": "人物姓名",
      "status": "人物在本章的状态",
      "emotions": "情感状态",
      "relationships": "关系变化"
    }
  ],
  "plotProgress": "情节推进要点",
  "previousChapterSummary": "对前一章的简要总结",
  "nextChapterHints": "对下一章的暗示或铺垫",
  "contextualNotes": "其他重要的上下文注释"
}

请确保返回的是有效的JSON格式。
`;

    try {
      const result = await rewriteText({
        originalText: analysisPrompt,
        rules: '请严格按照要求的JSON格式返回分析结果，不要添加任何其他内容。',
        chapterTitle: `第${chapterNumber}章分析`,
        chapterNumber: chapterNumber,
        model: 'gemini-2.5-flash-lite'
      });

      if (!result.success) {
        throw new Error(`章节分析失败: ${result.error}`);
      }

      // 解析AI返回的JSON
      let analysisData;
      try {
        const jsonMatch = result.rewrittenText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          analysisData = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error('无法找到JSON格式的分析结果');
        }
      } catch (parseError) {
        console.error('解析章节分析结果失败:', parseError);
        analysisData = {
          keyEvents: [],
          characterStates: [],
          plotProgress: '分析失败',
          previousChapterSummary: '',
          nextChapterHints: '',
          contextualNotes: '分析结果解析失败'
        };
      }

      // 检查是否已存在章节上下文
      const existingContext = chapterContextDb.getByChapter(novelId, chapterNumber);
      
      if (existingContext) {
        // 更新现有上下文
        return chapterContextDb.update(existingContext.id, {
          keyEvents: analysisData.keyEvents,
          characterStates: analysisData.characterStates,
          plotProgress: analysisData.plotProgress,
          previousChapterSummary: analysisData.previousChapterSummary,
          nextChapterHints: analysisData.nextChapterHints,
          contextualNotes: analysisData.contextualNotes
        })!;
      } else {
        // 创建新的章节上下文
        return chapterContextDb.create({
          novelId,
          chapterNumber,
          keyEvents: analysisData.keyEvents,
          characterStates: analysisData.characterStates,
          plotProgress: analysisData.plotProgress,
          previousChapterSummary: analysisData.previousChapterSummary,
          nextChapterHints: analysisData.nextChapterHints,
          contextualNotes: analysisData.contextualNotes
        });
      }

    } catch (error) {
      console.error(`第${chapterNumber}章分析失败:`, error);
      throw error;
    }
  }

  // 批量分析所有章节
  async analyzeAllChapters(novelId: string): Promise<ChapterContext[]> {
    const chapters = chapterDb.getByNovelId(novelId)
      .sort((a, b) => a.chapterNumber - b.chapterNumber);
    
    const results: ChapterContext[] = [];
    
    for (const chapter of chapters) {
      try {
        console.log(`正在分析第${chapter.chapterNumber}章: ${chapter.title}`);
        const context = await this.analyzeChapter(novelId, chapter.chapterNumber);
        results.push(context);
        
        // 添加延迟避免API限制
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (error) {
        console.error(`第${chapter.chapterNumber}章分析失败:`, error);
      }
    }
    
    return results;
  }
}

// 导出单例实例
export const contextAnalyzer = new ContextAnalyzer();
