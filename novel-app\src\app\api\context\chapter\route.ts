import { NextRequest, NextResponse } from 'next/server';
import { chapterContextDb } from '@/lib/database';

// GET - 获取章节上下文
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const novelId = searchParams.get('novelId');
    const chapterNumber = searchParams.get('chapterNumber');
    
    if (!novelId) {
      return NextResponse.json(
        { success: false, error: '小说ID不能为空' },
        { status: 400 }
      );
    }
    
    if (chapterNumber) {
      // 获取特定章节的上下文
      const context = chapterContextDb.getByChapter(novelId, parseInt(chapterNumber));
      
      if (!context) {
        return NextResponse.json(
          { success: false, error: '未找到章节上下文' },
          { status: 404 }
        );
      }
      
      return NextResponse.json({
        success: true,
        data: context,
      });
    } else {
      // 获取小说所有章节的上下文
      const contexts = chapterContextDb.getByNovelId(novelId);
      
      return NextResponse.json({
        success: true,
        data: contexts,
      });
    }
  } catch (error) {
    console.error('获取章节上下文失败:', error);
    return NextResponse.json(
      { success: false, error: '获取章节上下文失败' },
      { status: 500 }
    );
  }
}

// PUT - 更新章节上下文
export async function PUT(request: NextRequest) {
  try {
    const { novelId, chapterNumber, ...updates } = await request.json();
    
    if (!novelId || chapterNumber === undefined) {
      return NextResponse.json(
        { success: false, error: '小说ID和章节号不能为空' },
        { status: 400 }
      );
    }
    
    const existingContext = chapterContextDb.getByChapter(novelId, chapterNumber);
    if (!existingContext) {
      return NextResponse.json(
        { success: false, error: '未找到章节上下文' },
        { status: 404 }
      );
    }
    
    const updatedContext = chapterContextDb.update(existingContext.id, updates);
    
    if (!updatedContext) {
      return NextResponse.json(
        { success: false, error: '更新失败' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: updatedContext,
      message: '章节上下文更新成功'
    });
  } catch (error) {
    console.error('更新章节上下文失败:', error);
    return NextResponse.json(
      { success: false, error: '更新章节上下文失败' },
      { status: 500 }
    );
  }
}

// DELETE - 删除章节上下文
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const novelId = searchParams.get('novelId');
    const chapterNumber = searchParams.get('chapterNumber');
    
    if (!novelId || !chapterNumber) {
      return NextResponse.json(
        { success: false, error: '小说ID和章节号不能为空' },
        { status: 400 }
      );
    }
    
    const existingContext = chapterContextDb.getByChapter(novelId, parseInt(chapterNumber));
    if (!existingContext) {
      return NextResponse.json(
        { success: false, error: '未找到章节上下文' },
        { status: 404 }
      );
    }
    
    const deleted = chapterContextDb.delete(existingContext.id);
    
    if (!deleted) {
      return NextResponse.json(
        { success: false, error: '删除失败' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      success: true,
      message: '章节上下文删除成功'
    });
  } catch (error) {
    console.error('删除章节上下文失败:', error);
    return NextResponse.json(
      { success: false, error: '删除章节上下文失败' },
      { status: 500 }
    );
  }
}
