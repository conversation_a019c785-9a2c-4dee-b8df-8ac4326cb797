import { NextRequest, NextResponse } from 'next/server';
import { chapterDb, jobDb, novelDb } from '@/lib/database';
import { rewriteChapters } from '@/lib/gemini';
import { parseChapterRange } from '@/lib/novel-parser';
import { fileManager } from '@/lib/file-manager';
import path from 'path';

// POST - 创建改写任务
export async function POST(request: NextRequest) {
  try {
    const { novelId, chapterRange, rules, model, concurrency } = await request.json();

    if (!novelId || !chapterRange || !rules) {
      return NextResponse.json(
        { success: false, error: '参数不完整' },
        { status: 400 }
      );
    }

    // 验证模型和并发数参数
    const validModels = ['gemini-2.5-flash-lite', 'gemini-2.5-flash', 'gemini-1.5-pro'];
    const selectedModel = model && validModels.includes(model) ? model : 'gemini-2.5-flash-lite';
    const selectedConcurrency = concurrency && concurrency >= 1 && concurrency <= 10 ? concurrency : 3;

    // 获取小说信息
    const novel = novelDb.getById(novelId);
    if (!novel) {
      return NextResponse.json(
        { success: false, error: '小说不存在' },
        { status: 404 }
      );
    }

    // 获取所有章节
    const allChapters = chapterDb.getByNovelId(novelId);
    if (allChapters.length === 0) {
      return NextResponse.json(
        { success: false, error: '该小说没有章节' },
        { status: 404 }
      );
    }

    // 解析章节范围
    const chapterNumbers = parseChapterRange(chapterRange, allChapters.length);
    if (chapterNumbers.length === 0) {
      return NextResponse.json(
        { success: false, error: '无效的章节范围' },
        { status: 400 }
      );
    }

    // 获取要改写的章节
    const chaptersToRewrite = allChapters.filter(chapter =>
      chapterNumbers.includes(chapter.chapterNumber)
    );

    if (chaptersToRewrite.length === 0) {
      return NextResponse.json(
        { success: false, error: '没有找到指定的章节' },
        { status: 404 }
      );
    }

    // 创建改写任务
    const job = jobDb.create({
      novelId,
      chapters: chapterNumbers,
      ruleId: 'custom', // 暂时使用custom，后续可以关联到规则表
      status: 'pending',
      progress: 0,
      details: {
        totalChapters: chaptersToRewrite.length,
        completedChapters: 0,
        failedChapters: 0,
        totalTokensUsed: 0,
        totalProcessingTime: 0,
        averageTimePerChapter: 0,
        apiKeyStats: [],
        chapterResults: [],
        model: selectedModel,
        concurrency: selectedConcurrency,
      }
    });

    // 异步执行改写任务
    executeRewriteJob(job.id, chaptersToRewrite, rules, novel.title, selectedModel, selectedConcurrency);

    return NextResponse.json({
      success: true,
      data: {
        jobId: job.id,
        chaptersCount: chaptersToRewrite.length,
        message: '改写任务已创建，正在处理中...',
      },
    });

  } catch (error) {
    console.error('创建改写任务失败:', error);
    return NextResponse.json(
      { success: false, error: '创建改写任务失败' },
      { status: 500 }
    );
  }
}

// 异步执行改写任务 - 支持实时写入和详细进度
async function executeRewriteJob(
  jobId: string,
  chapters: Array<{
    id: string;
    novelId: string;
    chapterNumber: number;
    title: string;
    content: string;
    filename: string;
    createdAt: string;
  }>,
  rules: string,
  novelTitle: string,
  model: string = 'gemini-2.5-flash-lite',
  concurrency: number = 3
) {
  const startTime = Date.now();

  try {
    // 更新任务状态为处理中
    jobDb.update(jobId, {
      status: 'processing',
      progress: 0,
      details: {
        totalChapters: chapters.length,
        completedChapters: 0,
        failedChapters: 0,
        totalTokensUsed: 0,
        totalProcessingTime: 0,
        averageTimePerChapter: 0,
        apiKeyStats: jobDb.getById(jobId)?.details?.apiKeyStats || [],
        chapterResults: [],
        model: model,
        concurrency: concurrency,
      }
    });

    // 准备章节数据
    const chaptersData = chapters.map(chapter => ({
      content: chapter.content,
      title: chapter.title,
      number: chapter.chapterNumber,
    }));

    // 创建输出目录
    const outputDir = fileManager.getNovelRewrittenDir(novelTitle);

    // 执行改写（降低并发数以避免429错误，启用失败恢复）
    const results = await rewriteChapters(
      chaptersData,
      rules,
      // 进度回调 - 更新详细信息
      (progress, _currentChapter, details) => {
        const currentJob = jobDb.getById(jobId);
        if (currentJob && details) {
          jobDb.update(jobId, {
            progress: Math.round(progress),
            details: {
              totalChapters: currentJob.details?.totalChapters || chapters.length,
              completedChapters: details.completed,
              failedChapters: currentJob.details?.failedChapters || 0,
              totalTokensUsed: details.totalTokensUsed,
              totalProcessingTime: details.totalTime,
              averageTimePerChapter: details.averageTimePerChapter,
              apiKeyStats: details.apiKeyStats,
              chapterResults: currentJob.details?.chapterResults || [],
              model: model,
              concurrency: concurrency,
            }
          });
        }
      },
      // 章节完成回调 - 实时写入
      (chapterIndex, result) => {
        if (result.success) {
          // 立即写入完成的章节
          const chapter = chapters[chapterIndex];
          const filename = `chapter_${chapter.chapterNumber}_rewritten.txt`;
          const filePath = path.join(outputDir, filename);
          fileManager.writeFile(filePath, result.content);
        }

        // 更新章节结果到数据库
        const currentJob = jobDb.getById(jobId);
        if (currentJob?.details && result) {
          const chapterResults = [...(currentJob.details.chapterResults || [])];
          chapterResults[chapterIndex] = {
            chapterNumber: result.details?.chapterNumber || chapterIndex + 1,
            chapterTitle: result.details?.chapterTitle || `第${chapterIndex + 1}章`,
            success: result.success,
            error: result.error,
            apiKeyUsed: result.details?.apiKeyUsed,
            tokensUsed: result.details?.tokensUsed,
            processingTime: result.details?.processingTime,
            completedAt: new Date().toISOString(),
          };

          jobDb.update(jobId, {
            details: {
              ...currentJob.details,
              chapterResults,
              failedChapters: chapterResults.filter(r => r && !r.success).length,
            }
          });
        }
      },
      concurrency, // 使用用户选择的并发数量
      model, // 使用用户选择的模型
      true // 启用失败恢复机制
    );

    // 统计结果
    let successCount = 0;
    let totalTokensUsed = 0;
    const resultSummary: Array<{
      chapterNumber: number;
      chapterTitle: string;
      success: boolean;
      error?: string;
      apiKeyUsed?: string;
      tokensUsed?: number;
      processingTime?: number;
    }> = [];

    for (let i = 0; i < results.length; i++) {
      const result = results[i];
      const chapter = chapters[i];

      // 检查 result 是否为 null 或 undefined
      if (result && result.success) {
        successCount++;
      }

      if (result?.details?.tokensUsed) {
        totalTokensUsed += result.details.tokensUsed;
      }

      resultSummary.push({
        chapterNumber: chapter.chapterNumber,
        chapterTitle: chapter.title,
        success: result ? result.success : false,
        error: result?.error || '处理失败',
        apiKeyUsed: result?.details?.apiKeyUsed,
        tokensUsed: result?.details?.tokensUsed,
        processingTime: result?.details?.processingTime,
      });
    }

    // 保存详细的结果摘要
    const summaryPath = path.join(outputDir, 'rewrite_summary.json');
    const totalProcessingTime = Date.now() - startTime;

    const summaryData = JSON.stringify({
      jobId,
      novelTitle,
      rules,
      totalChapters: chapters.length,
      successCount,
      failedCount: chapters.length - successCount,
      totalTokensUsed,
      totalProcessingTime,
      averageTimePerChapter: totalProcessingTime / chapters.length,
      results: resultSummary,
      completedAt: new Date().toISOString(),
      model: model,
      concurrency: concurrency,
    }, null, 2);
    fileManager.writeFile(summaryPath, summaryData);

    // 更新任务状态为完成
    const currentJob = jobDb.getById(jobId);
    const finalStatus = successCount > 0 ? 'completed' : 'failed';

    jobDb.update(jobId, {
      status: finalStatus,
      progress: 100,
      result: `成功改写 ${successCount}/${chapters.length} 章节，结果保存在: ${outputDir}`,
      details: {
        totalChapters: chapters.length,
        completedChapters: successCount,
        failedChapters: chapters.length - successCount,
        totalTokensUsed,
        totalProcessingTime,
        averageTimePerChapter: chapters.length > 0 ? totalProcessingTime / chapters.length : 0,
        apiKeyStats: currentJob?.details?.apiKeyStats || [],
        chapterResults: resultSummary,
        model: model,
        concurrency: concurrency,
      }
    });

  } catch (error) {
    console.error('执行改写任务失败:', error);
    jobDb.update(jobId, {
      status: 'failed',
      result: `改写失败: ${error instanceof Error ? error.message : '未知错误'}`,
    });
  }
}


