// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap, AppRouteHandlerRoutes } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/types.js"
import type { NextRequest } from 'next/server.js'

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type RouteHandlerConfig<Route extends AppRouteHandlerRoutes = AppRouteHandlerRoutes> = {
  GET?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  POST?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PUT?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PATCH?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  DELETE?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  HEAD?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  OPTIONS?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
}


// Validate ../../src/app/help/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/help">> = Specific
  const handler = {} as typeof import("../../src/app/help/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/merge/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/merge">> = Specific
  const handler = {} as typeof import("../../src/app/merge/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/">> = Specific
  const handler = {} as typeof import("../../src/app/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/chapters/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/chapters">> = Specific
  const handler = {} as typeof import("../../src/app/api/chapters/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/characters/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/characters">> = Specific
  const handler = {} as typeof import("../../src/app/api/characters/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/gemini/reset/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/gemini/reset">> = Specific
  const handler = {} as typeof import("../../src/app/api/gemini/reset/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/gemini/stats/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/gemini/stats">> = Specific
  const handler = {} as typeof import("../../src/app/api/gemini/stats/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/gemini/test/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/gemini/test">> = Specific
  const handler = {} as typeof import("../../src/app/api/gemini/test/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/jobs/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/jobs">> = Specific
  const handler = {} as typeof import("../../src/app/api/jobs/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/merge/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/merge">> = Specific
  const handler = {} as typeof import("../../src/app/api/merge/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/novels/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/novels">> = Specific
  const handler = {} as typeof import("../../src/app/api/novels/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/presets/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/presets">> = Specific
  const handler = {} as typeof import("../../src/app/api/presets/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/rewrite/diagnostics/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/rewrite/diagnostics">> = Specific
  const handler = {} as typeof import("../../src/app/api/rewrite/diagnostics/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/rewrite/retry/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/rewrite/retry">> = Specific
  const handler = {} as typeof import("../../src/app/api/rewrite/retry/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/rewrite/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/rewrite">> = Specific
  const handler = {} as typeof import("../../src/app/api/rewrite/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/rules/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/rules">> = Specific
  const handler = {} as typeof import("../../src/app/api/rules/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}





// Validate ../../src/app/layout.tsx
{
  type __IsExpected<Specific extends LayoutConfig<"/">> = Specific
  const handler = {} as typeof import("../../src/app/layout.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}
