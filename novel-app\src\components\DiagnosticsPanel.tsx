'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>ert<PERSON>riangle, CheckCircle, XCircle, Info, RefreshCw } from 'lucide-react';

interface DiagnosticsData {
  jobInfo: {
    id: string;
    status: string;
    progress: number;
    totalChapters: number;
    completedChapters: number;
    failedChapters: number;
    model?: string;
    concurrency?: number;
  };
  apiKeyStatus: {
    stats: Array<{
      name: string;
      requestCount: number;
      weight: number;
      isAvailable: boolean;
      cooldownRemaining?: number;
    }>;
    connectionTest: {
      success: boolean;
      error?: string;
      details?: any;
    };
    recommendations: Array<{
      type: string;
      message: string;
      action: string;
    }>;
  };
  errorAnalysis: {
    totalFailures: number;
    errorTypes: Record<string, number>;
    apiKeyErrors: Record<string, number>;
    patterns: {
      timeoutErrors: number;
      contentErrors: number;
      networkErrors: number;
    };
    mostCommonError: string;
    problematicApiKey: string;
  };
  recommendations: Array<{
    type: string;
    category: string;
    message: string;
    actions: string[];
  }>;
  systemHealth: {
    timestamp: string;
    totalApiCalls: number;
    availableKeys: number;
    totalKeys: number;
  };
}

interface DiagnosticsPanelProps {
  jobId: string;
  isOpen: boolean;
  onClose: () => void;
}

export default function DiagnosticsPanel({ jobId, isOpen, onClose }: DiagnosticsPanelProps) {
  const [diagnostics, setDiagnostics] = useState<DiagnosticsData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchDiagnostics = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/rewrite/diagnostics?jobId=${jobId}`);
      const data = await response.json();
      
      if (data.success) {
        setDiagnostics(data.data);
      } else {
        setError(data.error);
      }
    } catch (err) {
      setError(`获取诊断信息失败: ${err instanceof Error ? err.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen && jobId) {
      fetchDiagnostics();
    }
  }, [isOpen, jobId]);

  const getRecommendationIcon = (type: string) => {
    switch (type) {
      case 'error': return <XCircle className="w-4 h-4 text-red-500" />;
      case 'warning': return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'info': return <Info className="w-4 h-4 text-blue-500" />;
      default: return <CheckCircle className="w-4 h-4 text-green-500" />;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-xl font-semibold">任务诊断报告</h2>
          <div className="flex gap-2">
            <button
              onClick={fetchDiagnostics}
              disabled={loading}
              className="p-2 text-gray-500 hover:text-gray-700 disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            </button>
            <button
              onClick={onClose}
              className="p-2 text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>
        </div>

        <div className="p-4 overflow-y-auto max-h-[calc(90vh-80px)]">
          {loading && (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              <span className="ml-2">正在获取诊断信息...</span>
            </div>
          )}

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
              <div className="flex items-center text-red-700">
                <XCircle className="w-5 h-5 mr-2" />
                <span>{error}</span>
              </div>
            </div>
          )}

          {diagnostics && (
            <div className="space-y-6">
              {/* 任务概览 */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold mb-3">任务概览</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">状态:</span>
                    <span className="ml-2 font-medium">{diagnostics.jobInfo.status}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">进度:</span>
                    <span className="ml-2 font-medium">{diagnostics.jobInfo.progress}%</span>
                  </div>
                  <div>
                    <span className="text-gray-600">成功:</span>
                    <span className="ml-2 font-medium text-green-600">
                      {diagnostics.jobInfo.completedChapters}/{diagnostics.jobInfo.totalChapters}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600">失败:</span>
                    <span className="ml-2 font-medium text-red-600">
                      {diagnostics.jobInfo.failedChapters}
                    </span>
                  </div>
                </div>
              </div>

              {/* API Key状态 */}
              <div className="bg-blue-50 rounded-lg p-4">
                <h3 className="font-semibold mb-3">API Key状态</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium mb-2">连接测试</h4>
                    <div className={`flex items-center ${
                      diagnostics.apiKeyStatus.connectionTest.success ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {diagnostics.apiKeyStatus.connectionTest.success ? (
                        <CheckCircle className="w-4 h-4 mr-2" />
                      ) : (
                        <XCircle className="w-4 h-4 mr-2" />
                      )}
                      <span className="text-sm">
                        {diagnostics.apiKeyStatus.connectionTest.success ? '连接正常' : '连接失败'}
                      </span>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">Key统计</h4>
                    <div className="text-sm text-gray-600">
                      可用: {diagnostics.systemHealth.availableKeys}/{diagnostics.systemHealth.totalKeys} |
                      总调用: {diagnostics.systemHealth.totalApiCalls}
                    </div>
                  </div>
                </div>
                
                <div className="mt-3">
                  <h4 className="font-medium mb-2">详细状态</h4>
                  <div className="space-y-1 text-sm">
                    {diagnostics.apiKeyStatus.stats.map((key, index) => (
                      <div key={index} className="flex justify-between items-center">
                        <span>{key.name}</span>
                        <div className="flex items-center gap-2">
                          <span className="text-gray-600">{key.requestCount} 次调用</span>
                          <span className={`px-2 py-1 rounded text-xs ${
                            key.isAvailable ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
                          }`}>
                            {key.isAvailable ? '可用' : '冷却中'}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* 错误分析 */}
              {diagnostics.errorAnalysis.totalFailures > 0 && (
                <div className="bg-red-50 rounded-lg p-4">
                  <h3 className="font-semibold mb-3">错误分析</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium mb-2">错误类型分布</h4>
                      <div className="space-y-1 text-sm">
                        {Object.entries(diagnostics.errorAnalysis.errorTypes).map(([type, count]) => (
                          <div key={type} className="flex justify-between">
                            <span>{type}</span>
                            <span className="font-medium">{count}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium mb-2">关键信息</h4>
                      <div className="space-y-1 text-sm">
                        <div>最常见错误: <span className="font-medium">{diagnostics.errorAnalysis.mostCommonError}</span></div>
                        <div>问题API Key: <span className="font-medium">{diagnostics.errorAnalysis.problematicApiKey}</span></div>
                        <div>超时错误: <span className="font-medium">{diagnostics.errorAnalysis.patterns.timeoutErrors}</span></div>
                        <div>内容错误: <span className="font-medium">{diagnostics.errorAnalysis.patterns.contentErrors}</span></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 建议 */}
              <div className="bg-yellow-50 rounded-lg p-4">
                <h3 className="font-semibold mb-3">改进建议</h3>
                <div className="space-y-3">
                  {diagnostics.recommendations.map((rec, index) => (
                    <div key={index} className="border border-yellow-200 rounded p-3">
                      <div className="flex items-center mb-2">
                        {getRecommendationIcon(rec.type)}
                        <span className="ml-2 font-medium">{rec.category}</span>
                      </div>
                      <p className="text-sm text-gray-700 mb-2">{rec.message}</p>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {rec.actions.map((action, actionIndex) => (
                          <li key={actionIndex} className="flex items-start">
                            <span className="mr-2">•</span>
                            <span>{action}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
