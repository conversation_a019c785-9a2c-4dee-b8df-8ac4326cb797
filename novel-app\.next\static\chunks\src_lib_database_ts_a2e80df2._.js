(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/src/lib/database.ts [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "chapterContextDb",
    ()=>chapterContextDb,
    "chapterDb",
    ()=>chapterDb,
    "characterDb",
    ()=>characterDb,
    "jobDb",
    ()=>jobDb,
    "novelContextDb",
    ()=>novelContextDb,
    "novelDb",
    ()=>novelDb,
    "presetDb",
    ()=>presetDb,
    "ruleDb",
    ()=>ruleDb
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
(()=>{
    const e = new Error("Cannot find module 'fs'");
    e.code = 'MODULE_NOT_FOUND';
    throw e;
})();
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$path$2d$browserify$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/path-browserify/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$crypto$2d$browserify$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/crypto-browserify/index.js [app-client] (ecmascript)");
;
;
;
// 数据存储路径
const DATA_DIR = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$path$2d$browserify$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].join(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].cwd(), 'data');
const NOVELS_FILE = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$path$2d$browserify$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].join(DATA_DIR, 'novels.json');
_c = NOVELS_FILE;
const CHAPTERS_FILE = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$path$2d$browserify$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].join(DATA_DIR, 'chapters.json');
_c1 = CHAPTERS_FILE;
const RULES_FILE = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$path$2d$browserify$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].join(DATA_DIR, 'rewrite_rules.json');
_c2 = RULES_FILE;
const JOBS_FILE = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$path$2d$browserify$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].join(DATA_DIR, 'rewrite_jobs.json');
_c3 = JOBS_FILE;
const CHARACTERS_FILE = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$path$2d$browserify$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].join(DATA_DIR, 'characters.json');
_c4 = CHARACTERS_FILE;
const PRESETS_FILE = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$path$2d$browserify$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].join(DATA_DIR, 'presets.json');
_c5 = PRESETS_FILE;
const NOVEL_CONTEXTS_FILE = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$path$2d$browserify$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].join(DATA_DIR, 'novel-contexts.json');
_c6 = NOVEL_CONTEXTS_FILE;
const CHAPTER_CONTEXTS_FILE = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$path$2d$browserify$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].join(DATA_DIR, 'chapter-contexts.json');
_c7 = CHAPTER_CONTEXTS_FILE;
// 确保数据目录存在
function ensureDataDir() {
    if (!fs.existsSync(DATA_DIR)) {
        fs.mkdirSync(DATA_DIR, {
            recursive: true
        });
    }
}
// 读取JSON文件
function readJsonFile(filePath) {
    ensureDataDir();
    if (!fs.existsSync(filePath)) {
        return [];
    }
    try {
        const data = fs.readFileSync(filePath, 'utf-8');
        return JSON.parse(data);
    } catch (error) {
        console.error("Error reading ".concat(filePath, ":"), error);
        return [];
    }
}
// 写入JSON文件
function writeJsonFile(filePath, data) {
    ensureDataDir();
    try {
        fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf-8');
    } catch (error) {
        console.error("Error writing ".concat(filePath, ":"), error);
        throw error;
    }
}
// 生成唯一ID
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
}
// 基于内容生成确定性ID
function generateDeterministicId(content) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$crypto$2d$browserify$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createHash('md5').update(content).digest('hex').substring(0, 18);
}
const novelDb = {
    getAll: ()=>readJsonFile(NOVELS_FILE),
    getById: (id)=>{
        const novels = readJsonFile(NOVELS_FILE);
        return novels.find((novel)=>novel.id === id);
    },
    create: (novel)=>{
        const novels = readJsonFile(NOVELS_FILE);
        // 使用书名生成确定性ID
        const novelId = generateDeterministicId(novel.title);
        // 检查是否已存在相同ID的小说
        const existingNovel = novels.find((n)=>n.id === novelId);
        if (existingNovel) {
            // 如果已存在，更新现有记录
            existingNovel.filename = novel.filename;
            existingNovel.chapterCount = novel.chapterCount;
            writeJsonFile(NOVELS_FILE, novels);
            return existingNovel;
        }
        const newNovel = {
            ...novel,
            id: novelId,
            createdAt: new Date().toISOString()
        };
        novels.push(newNovel);
        writeJsonFile(NOVELS_FILE, novels);
        return newNovel;
    },
    update: (id, updates)=>{
        const novels = readJsonFile(NOVELS_FILE);
        const index = novels.findIndex((novel)=>novel.id === id);
        if (index === -1) return null;
        novels[index] = {
            ...novels[index],
            ...updates
        };
        writeJsonFile(NOVELS_FILE, novels);
        return novels[index];
    },
    delete: (id)=>{
        const novels = readJsonFile(NOVELS_FILE);
        const index = novels.findIndex((novel)=>novel.id === id);
        if (index === -1) return false;
        novels.splice(index, 1);
        writeJsonFile(NOVELS_FILE, novels);
        return true;
    }
};
const chapterDb = {
    getAll: ()=>readJsonFile(CHAPTERS_FILE),
    getByNovelId: (novelId)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        return chapters.filter((chapter)=>chapter.novelId === novelId);
    },
    getById: (id)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        return chapters.find((chapter)=>chapter.id === id);
    },
    create: (chapter)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        const newChapter = {
            ...chapter,
            id: generateId(),
            createdAt: new Date().toISOString()
        };
        chapters.push(newChapter);
        writeJsonFile(CHAPTERS_FILE, chapters);
        return newChapter;
    },
    createBatch: (chapters)=>{
        const existingChapters = readJsonFile(CHAPTERS_FILE);
        const newChapters = chapters.map((chapter)=>({
                ...chapter,
                id: generateId(),
                createdAt: new Date().toISOString()
            }));
        existingChapters.push(...newChapters);
        writeJsonFile(CHAPTERS_FILE, existingChapters);
        return newChapters;
    },
    delete: (id)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        const index = chapters.findIndex((chapter)=>chapter.id === id);
        if (index === -1) return false;
        chapters.splice(index, 1);
        writeJsonFile(CHAPTERS_FILE, chapters);
        return true;
    },
    deleteByNovelId: (novelId)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        const filteredChapters = chapters.filter((chapter)=>chapter.novelId !== novelId);
        writeJsonFile(CHAPTERS_FILE, filteredChapters);
        return true;
    }
};
const ruleDb = {
    getAll: ()=>readJsonFile(RULES_FILE),
    getById: (id)=>{
        const rules = readJsonFile(RULES_FILE);
        return rules.find((rule)=>rule.id === id);
    },
    create: (rule)=>{
        const rules = readJsonFile(RULES_FILE);
        const newRule = {
            ...rule,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        rules.push(newRule);
        writeJsonFile(RULES_FILE, rules);
        return newRule;
    },
    update: (id, updates)=>{
        const rules = readJsonFile(RULES_FILE);
        const index = rules.findIndex((rule)=>rule.id === id);
        if (index === -1) return null;
        rules[index] = {
            ...rules[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        writeJsonFile(RULES_FILE, rules);
        return rules[index];
    },
    delete: (id)=>{
        const rules = readJsonFile(RULES_FILE);
        const index = rules.findIndex((rule)=>rule.id === id);
        if (index === -1) return false;
        rules.splice(index, 1);
        writeJsonFile(RULES_FILE, rules);
        return true;
    }
};
const jobDb = {
    getAll: ()=>readJsonFile(JOBS_FILE),
    getById: (id)=>{
        const jobs = readJsonFile(JOBS_FILE);
        return jobs.find((job)=>job.id === id);
    },
    create: (job)=>{
        const jobs = readJsonFile(JOBS_FILE);
        const newJob = {
            ...job,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        jobs.push(newJob);
        writeJsonFile(JOBS_FILE, jobs);
        return newJob;
    },
    update: (id, updates)=>{
        const jobs = readJsonFile(JOBS_FILE);
        const index = jobs.findIndex((job)=>job.id === id);
        if (index === -1) return null;
        jobs[index] = {
            ...jobs[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        writeJsonFile(JOBS_FILE, jobs);
        return jobs[index];
    },
    delete: (id)=>{
        const jobs = readJsonFile(JOBS_FILE);
        const index = jobs.findIndex((job)=>job.id === id);
        if (index === -1) return false;
        jobs.splice(index, 1);
        writeJsonFile(JOBS_FILE, jobs);
        return true;
    }
};
const characterDb = {
    getAll: ()=>readJsonFile(CHARACTERS_FILE),
    getByNovelId: (novelId)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        return characters.filter((character)=>character.novelId === novelId);
    },
    getById: (id)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        return characters.find((character)=>character.id === id);
    },
    create: (character)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        const newCharacter = {
            ...character,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        characters.push(newCharacter);
        writeJsonFile(CHARACTERS_FILE, characters);
        return newCharacter;
    },
    update: (id, updates)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        const index = characters.findIndex((character)=>character.id === id);
        if (index === -1) return null;
        characters[index] = {
            ...characters[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        writeJsonFile(CHARACTERS_FILE, characters);
        return characters[index];
    },
    delete: (id)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        const index = characters.findIndex((character)=>character.id === id);
        if (index === -1) return false;
        characters.splice(index, 1);
        writeJsonFile(CHARACTERS_FILE, characters);
        return true;
    },
    deleteByNovelId: (novelId)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        const filteredCharacters = characters.filter((character)=>character.novelId !== novelId);
        writeJsonFile(CHARACTERS_FILE, filteredCharacters);
        return true;
    }
};
const presetDb = {
    getAll: ()=>readJsonFile(PRESETS_FILE),
    getById: (id)=>{
        const presets = readJsonFile(PRESETS_FILE);
        return presets.find((preset)=>preset.id === id);
    },
    create: (preset)=>{
        const presets = readJsonFile(PRESETS_FILE);
        const newPreset = {
            ...preset,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        presets.push(newPreset);
        writeJsonFile(PRESETS_FILE, presets);
        return newPreset;
    },
    update: (id, updates)=>{
        const presets = readJsonFile(PRESETS_FILE);
        const index = presets.findIndex((preset)=>preset.id === id);
        if (index === -1) return null;
        presets[index] = {
            ...presets[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        writeJsonFile(PRESETS_FILE, presets);
        return presets[index];
    },
    delete: (id)=>{
        const presets = readJsonFile(PRESETS_FILE);
        const index = presets.findIndex((preset)=>preset.id === id);
        if (index === -1) return false;
        presets.splice(index, 1);
        writeJsonFile(PRESETS_FILE, presets);
        return true;
    }
};
const novelContextDb = {
    getAll: ()=>readJsonFile(NOVEL_CONTEXTS_FILE),
    getByNovelId: (novelId)=>{
        const contexts = readJsonFile(NOVEL_CONTEXTS_FILE);
        return contexts.find((context)=>context.novelId === novelId);
    },
    getById: (id)=>{
        const contexts = readJsonFile(NOVEL_CONTEXTS_FILE);
        return contexts.find((context)=>context.id === id);
    },
    create: (context)=>{
        const contexts = readJsonFile(NOVEL_CONTEXTS_FILE);
        const newContext = {
            ...context,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        contexts.push(newContext);
        writeJsonFile(NOVEL_CONTEXTS_FILE, contexts);
        return newContext;
    },
    update: (id, updates)=>{
        const contexts = readJsonFile(NOVEL_CONTEXTS_FILE);
        const index = contexts.findIndex((context)=>context.id === id);
        if (index === -1) return undefined;
        contexts[index] = {
            ...contexts[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        writeJsonFile(NOVEL_CONTEXTS_FILE, contexts);
        return contexts[index];
    },
    delete: (id)=>{
        const contexts = readJsonFile(NOVEL_CONTEXTS_FILE);
        const index = contexts.findIndex((context)=>context.id === id);
        if (index === -1) return false;
        contexts.splice(index, 1);
        writeJsonFile(NOVEL_CONTEXTS_FILE, contexts);
        return true;
    }
};
const chapterContextDb = {
    getAll: ()=>readJsonFile(CHAPTER_CONTEXTS_FILE),
    getByNovelId: (novelId)=>{
        const contexts = readJsonFile(CHAPTER_CONTEXTS_FILE);
        return contexts.filter((context)=>context.novelId === novelId);
    },
    getByChapter: (novelId, chapterNumber)=>{
        const contexts = readJsonFile(CHAPTER_CONTEXTS_FILE);
        return contexts.find((context)=>context.novelId === novelId && context.chapterNumber === chapterNumber);
    },
    getById: (id)=>{
        const contexts = readJsonFile(CHAPTER_CONTEXTS_FILE);
        return contexts.find((context)=>context.id === id);
    },
    create: (context)=>{
        const contexts = readJsonFile(CHAPTER_CONTEXTS_FILE);
        const newContext = {
            ...context,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        contexts.push(newContext);
        writeJsonFile(CHAPTER_CONTEXTS_FILE, contexts);
        return newContext;
    },
    update: (id, updates)=>{
        const contexts = readJsonFile(CHAPTER_CONTEXTS_FILE);
        const index = contexts.findIndex((context)=>context.id === id);
        if (index === -1) return undefined;
        contexts[index] = {
            ...contexts[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        writeJsonFile(CHAPTER_CONTEXTS_FILE, contexts);
        return contexts[index];
    },
    delete: (id)=>{
        const contexts = readJsonFile(CHAPTER_CONTEXTS_FILE);
        const index = contexts.findIndex((context)=>context.id === id);
        if (index === -1) return false;
        contexts.splice(index, 1);
        writeJsonFile(CHAPTER_CONTEXTS_FILE, contexts);
        return true;
    },
    // 获取章节的上下文窗口（前后几章的上下文）
    getContextWindow: function(novelId, chapterNumber) {
        let windowSize = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 2;
        const contexts = readJsonFile(CHAPTER_CONTEXTS_FILE);
        const novelContexts = contexts.filter((context)=>context.novelId === novelId);
        const startChapter = Math.max(1, chapterNumber - windowSize);
        const endChapter = chapterNumber + windowSize;
        return novelContexts.filter((context)=>context.chapterNumber >= startChapter && context.chapterNumber <= endChapter).sort((a, b)=>a.chapterNumber - b.chapterNumber);
    }
};
var _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7;
__turbopack_context__.k.register(_c, "NOVELS_FILE");
__turbopack_context__.k.register(_c1, "CHAPTERS_FILE");
__turbopack_context__.k.register(_c2, "RULES_FILE");
__turbopack_context__.k.register(_c3, "JOBS_FILE");
__turbopack_context__.k.register(_c4, "CHARACTERS_FILE");
__turbopack_context__.k.register(_c5, "PRESETS_FILE");
__turbopack_context__.k.register(_c6, "NOVEL_CONTEXTS_FILE");
__turbopack_context__.k.register(_c7, "CHAPTER_CONTEXTS_FILE");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
]);

//# sourceMappingURL=src_lib_database_ts_a2e80df2._.js.map