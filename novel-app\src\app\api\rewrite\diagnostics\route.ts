import { NextRequest, NextResponse } from 'next/server';
import { jobDb } from '@/lib/database';
import { getApiKeyStats, testGeminiConnection } from '@/lib/gemini';

// GET - 获取改写任务的诊断信息
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');

    if (!jobId) {
      return NextResponse.json(
        { success: false, error: '缺少任务ID' },
        { status: 400 }
      );
    }

    // 获取任务信息
    const job = jobDb.getById(jobId);
    if (!job) {
      return NextResponse.json(
        { success: false, error: '任务不存在' },
        { status: 404 }
      );
    }

    // 获取API Key统计信息
    const apiKeyStats = getApiKeyStats();

    // 测试API连接
    const connectionTest = await testGeminiConnection();

    // 分析失败章节
    const failedChapters = job.details?.chapterResults?.filter(r => r && !r.success) || [];
    const errorAnalysis = analyzeErrors(failedChapters);

    // 生成诊断报告
    const diagnostics = {
      jobInfo: {
        id: job.id,
        status: job.status,
        progress: job.progress,
        totalChapters: job.details?.totalChapters || 0,
        completedChapters: job.details?.completedChapters || 0,
        failedChapters: job.details?.failedChapters || 0,
        model: job.details?.model,
        concurrency: job.details?.concurrency,
      },
      apiKeyStatus: {
        stats: apiKeyStats,
        connectionTest,
        recommendations: generateApiKeyRecommendations(apiKeyStats),
      },
      errorAnalysis,
      recommendations: generateRecommendations(job, errorAnalysis, apiKeyStats),
      systemHealth: {
        timestamp: new Date().toISOString(),
        totalApiCalls: apiKeyStats.reduce((sum, key) => sum + key.requestCount, 0),
        availableKeys: apiKeyStats.filter(key => key.isAvailable).length,
        totalKeys: apiKeyStats.length,
      }
    };

    return NextResponse.json({
      success: true,
      data: diagnostics,
    });

  } catch (error) {
    console.error('获取诊断信息失败:', error);
    return NextResponse.json(
      { success: false, error: '获取诊断信息失败' },
      { status: 500 }
    );
  }
}

// 分析错误模式
function analyzeErrors(failedChapters: any[]) {
  const errorTypes: Record<string, number> = {};
  const apiKeyErrors: Record<string, number> = {};
  const timeoutErrors = [];
  const contentErrors = [];
  const networkErrors = [];

  for (const chapter of failedChapters) {
    if (!chapter.error) continue;

    const error = chapter.error.toLowerCase();
    
    // 统计错误类型
    if (error.includes('限流') || error.includes('429')) {
      errorTypes['API限流'] = (errorTypes['API限流'] || 0) + 1;
    } else if (error.includes('超时') || error.includes('timeout')) {
      errorTypes['请求超时'] = (errorTypes['请求超时'] || 0) + 1;
      timeoutErrors.push(chapter);
    } else if (error.includes('没有收到有效的响应') || error.includes('响应内容格式错误')) {
      errorTypes['响应格式错误'] = (errorTypes['响应格式错误'] || 0) + 1;
      contentErrors.push(chapter);
    } else if (error.includes('网络错误') || error.includes('fetch')) {
      errorTypes['网络错误'] = (errorTypes['网络错误'] || 0) + 1;
      networkErrors.push(chapter);
    } else if (error.includes('安全过滤')) {
      errorTypes['内容过滤'] = (errorTypes['内容过滤'] || 0) + 1;
    } else {
      errorTypes['其他错误'] = (errorTypes['其他错误'] || 0) + 1;
    }

    // 统计API Key错误
    if (chapter.apiKeyUsed) {
      apiKeyErrors[chapter.apiKeyUsed] = (apiKeyErrors[chapter.apiKeyUsed] || 0) + 1;
    }
  }

  return {
    totalFailures: failedChapters.length,
    errorTypes,
    apiKeyErrors,
    patterns: {
      timeoutErrors: timeoutErrors.length,
      contentErrors: contentErrors.length,
      networkErrors: networkErrors.length,
    },
    mostCommonError: Object.entries(errorTypes).sort(([,a], [,b]) => b - a)[0]?.[0] || 'unknown',
    problematicApiKey: Object.entries(apiKeyErrors).sort(([,a], [,b]) => b - a)[0]?.[0] || 'none',
  };
}

// 生成API Key建议
function generateApiKeyRecommendations(apiKeyStats: any[]) {
  const recommendations = [];
  
  const unavailableKeys = apiKeyStats.filter(key => !key.isAvailable);
  if (unavailableKeys.length > 0) {
    recommendations.push({
      type: 'warning',
      message: `${unavailableKeys.length} 个API Key当前不可用，可能处于冷却期`,
      action: '等待冷却期结束或检查API配额'
    });
  }

  const overusedKeys = apiKeyStats.filter(key => key.requestCount > 100);
  if (overusedKeys.length > 0) {
    recommendations.push({
      type: 'info',
      message: `${overusedKeys.length} 个API Key使用频率较高`,
      action: '考虑添加更多API Key以分散负载'
    });
  }

  if (apiKeyStats.every(key => !key.isAvailable)) {
    recommendations.push({
      type: 'error',
      message: '所有API Key都不可用',
      action: '检查API配额或等待冷却期结束'
    });
  }

  return recommendations;
}

// 生成总体建议
function generateRecommendations(job: any, errorAnalysis: any, apiKeyStats: any[]) {
  const recommendations = [];

  // 基于错误分析的建议
  if (errorAnalysis.mostCommonError === 'API限流') {
    recommendations.push({
      type: 'warning',
      category: '限流问题',
      message: 'API限流是主要问题',
      actions: [
        '降低并发数量（建议设为1-2）',
        '增加请求间隔时间',
        '使用重试功能处理失败章节',
        '考虑在API使用量较低的时间段进行处理'
      ]
    });
  }

  if (errorAnalysis.patterns.contentErrors > 0) {
    recommendations.push({
      type: 'error',
      category: '响应问题',
      message: '检测到响应内容格式错误',
      actions: [
        '检查改写规则是否过于复杂',
        '尝试简化提示词',
        '使用重试功能重新处理失败章节',
        '考虑切换到更稳定的模型'
      ]
    });
  }

  if (errorAnalysis.patterns.networkErrors > 0) {
    recommendations.push({
      type: 'warning',
      category: '网络问题',
      message: '检测到网络连接问题',
      actions: [
        '检查网络连接稳定性',
        '增加请求超时时间',
        '使用重试功能处理失败章节'
      ]
    });
  }

  // 基于任务状态的建议
  const failureRate = job.details?.failedChapters / job.details?.totalChapters || 0;
  if (failureRate > 0.1) {
    recommendations.push({
      type: 'warning',
      category: '高失败率',
      message: `失败率较高 (${Math.round(failureRate * 100)}%)`,
      actions: [
        '使用失败章节重试功能',
        '检查API Key配额状态',
        '考虑调整改写策略',
        '分批处理章节以降低负载'
      ]
    });
  }

  // 基于API Key状态的建议
  const availableKeyCount = apiKeyStats.filter(key => key.isAvailable).length;
  if (availableKeyCount < 2) {
    recommendations.push({
      type: 'error',
      category: 'API Key不足',
      message: '可用API Key数量不足',
      actions: [
        '等待API Key冷却期结束',
        '检查API配额是否用尽',
        '考虑添加更多API Key',
        '降低处理并发数'
      ]
    });
  }

  return recommendations;
}
