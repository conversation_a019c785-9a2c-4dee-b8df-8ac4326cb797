// 测试上下文记忆系统
const fs = require('fs');
const path = require('path');

// 模拟测试数据
const testNovelContent = `第一章 初遇

李明是一个普通的大学生，今天他在图书馆遇到了美丽的女孩小雨。小雨正在看一本关于古代文学的书，李明鼓起勇气上前搭话。

"你好，我是李明，也对古代文学很感兴趣。"

小雨抬起头，露出了甜美的笑容："你好，我是小雨。你也喜欢古代文学吗？"

两人开始了愉快的交谈，李明发现小雨不仅美丽，而且很有才华。

第二章 深入了解

经过几天的相处，李明和小雨成为了好朋友。他们经常一起在图书馆学习，讨论各种文学作品。

李明发现自己越来越喜欢小雨了，但他不确定小雨对他的感觉。

"小雨，你觉得我们的友谊怎么样？"李明试探性地问道。

小雨脸红了："我觉得我们相处得很好，你是一个很特别的人。"

李明心中一阵欣喜，但他还是不敢表白。

第三章 表白

终于，在一个月圆之夜，李明决定向小雨表白。他们在校园的湖边散步。

"小雨，我有话想对你说。"李明紧张地说道。

"什么话？"小雨好奇地看着他。

"我喜欢你，从第一次见面就喜欢上了你。你愿意做我的女朋友吗？"

小雨沉默了一会儿，然后点了点头："我也喜欢你，李明。"

两人拥抱在一起，月光洒在湖面上，一切都显得那么美好。`;

async function testContextSystem() {
  console.log('开始测试上下文记忆系统...\n');

  try {
    // 1. 测试小说解析和创建
    console.log('1. 测试小说解析...');
    
    // 创建测试小说文件
    const testNovelPath = path.join(__dirname, 'test-novel.txt');
    fs.writeFileSync(testNovelPath, testNovelContent, 'utf-8');
    
    // 解析小说
    const parseResponse = await fetch('http://localhost:3000/api/novels/parse', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ filePath: testNovelPath })
    });
    
    const parseResult = await parseResponse.json();
    console.log('解析结果:', parseResult.success ? '成功' : '失败');
    
    if (!parseResult.success) {
      console.error('解析失败:', parseResult.error);
      return;
    }
    
    const novelId = parseResult.data.novel.id;
    console.log('小说ID:', novelId);
    console.log('章节数量:', parseResult.data.chapters.length);
    
    // 2. 测试上下文分析
    console.log('\n2. 测试上下文分析...');
    
    const analyzeResponse = await fetch('http://localhost:3000/api/context/analyze', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        novelId: novelId,
        analyzeChapters: true 
      })
    });
    
    const analyzeResult = await analyzeResponse.json();
    console.log('分析结果:', analyzeResult.success ? '成功' : '失败');
    
    if (analyzeResult.success) {
      console.log('小说上下文摘要:', analyzeResult.data.novelContext.summary.substring(0, 100) + '...');
      console.log('主要人物数量:', analyzeResult.data.novelContext.mainCharacters.length);
      console.log('章节上下文数量:', analyzeResult.data.chapterContexts.length);
    } else {
      console.error('分析失败:', analyzeResult.error);
    }
    
    // 3. 测试上下文查询
    console.log('\n3. 测试上下文查询...');
    
    const contextResponse = await fetch(`http://localhost:3000/api/context/novel?novelId=${novelId}`);
    const contextResult = await contextResponse.json();
    
    console.log('上下文查询结果:', contextResult.success ? '成功' : '失败');
    
    if (contextResult.success) {
      console.log('写作风格:', contextResult.data.writingStyle);
      console.log('世界观设定:', contextResult.data.worldSetting);
    }
    
    // 4. 测试章节上下文窗口
    console.log('\n4. 测试章节上下文窗口...');
    
    const windowResponse = await fetch(`http://localhost:3000/api/context/window?novelId=${novelId}&chapterNumber=2&windowSize=1`);
    const windowResult = await windowResponse.json();
    
    console.log('上下文窗口查询结果:', windowResult.success ? '成功' : '失败');
    
    if (windowResult.success) {
      console.log('窗口大小:', windowResult.data.windowSize);
      console.log('上下文数量:', windowResult.data.totalContexts);
    }
    
    // 5. 测试带上下文的重写
    console.log('\n5. 测试带上下文的重写...');
    
    const rewriteResponse = await fetch('http://localhost:3000/api/rewrite', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        novelId: novelId,
        chapterNumbers: [1],
        rules: '请将这个章节改写得更加浪漫，增加更多的心理描写和情感细节。',
        model: 'gemini-2.5-flash-lite'
      })
    });
    
    const rewriteResult = await rewriteResponse.json();
    console.log('重写任务创建结果:', rewriteResult.success ? '成功' : '失败');
    
    if (rewriteResult.success) {
      console.log('任务ID:', rewriteResult.data.jobId);
      console.log('等待重写完成...');
      
      // 等待一段时间让重写完成
      await new Promise(resolve => setTimeout(resolve, 30000));
      
      // 查询重写结果
      const jobResponse = await fetch(`http://localhost:3000/api/jobs/${rewriteResult.data.jobId}`);
      const jobResult = await jobResponse.json();
      
      if (jobResult.success) {
        console.log('重写状态:', jobResult.data.status);
        console.log('重写结果:', jobResult.data.result);
      }
    }
    
    console.log('\n测试完成！');
    
    // 清理测试文件
    if (fs.existsSync(testNovelPath)) {
      fs.unlinkSync(testNovelPath);
    }
    
  } catch (error) {
    console.error('测试过程中出现错误:', error);
  }
}

// 运行测试
if (require.main === module) {
  testContextSystem().catch(console.error);
}

module.exports = { testContextSystem };
