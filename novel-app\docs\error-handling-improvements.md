# 改写错误处理和恢复机制改进

## 问题描述

在小说章节改写过程中，可能会遇到以下问题导致章节内容丢失：

1. **API限流错误 (429)**：当API调用频率过高时，会触发限流
2. **响应格式错误**：API返回空响应或格式不正确的响应
3. **网络超时**：网络连接不稳定导致请求超时
4. **API配额用尽**：API Key的使用配额达到限制

这些问题会导致"成功改写 39/40 章节"的情况，失败的章节内容会丢失。

## 解决方案

### 1. 增强的错误处理机制

#### 改进的重试策略
- **增加重试次数**：从3次增加到5次
- **指数退避算法**：重试间隔时间逐渐增加 (2s, 4s, 8s, 16s, 32s)
- **智能等待**：根据API Key冷却时间和重试次数动态调整等待时间
- **请求超时控制**：设置60秒超时，防止无限等待

#### 详细错误信息
- **分类错误类型**：区分限流、超时、网络、格式等不同错误
- **记录详细日志**：包含错误发生的具体原因和上下文
- **API Key追踪**：记录每个错误对应的API Key，便于问题定位

### 2. 失败恢复机制

#### 自动恢复
- **批量处理后自动重试**：在所有章节处理完成后，自动重试失败的章节
- **保守重试策略**：失败恢复时使用串行处理，避免再次触发限流
- **延迟重试**：在重试前等待更长时间，让API Key充分冷却

#### 手动重试功能
- **失败章节展示**：清晰显示所有失败的章节及其错误原因
- **选择性重试**：用户可以选择特定章节进行重试
- **重试进度跟踪**：实时显示重试进度和结果

### 3. 智能诊断系统

#### 任务诊断面板
- **错误模式分析**：分析失败章节的错误类型和分布
- **API Key状态监控**：实时显示所有API Key的使用情况和可用性
- **性能统计**：显示处理时间、Token使用量等性能指标
- **改进建议**：基于错误分析提供具体的改进建议

#### 预防性监控
- **API配额监控**：实时监控API Key的使用配额
- **连接状态检测**：定期测试API连接状态
- **负载均衡优化**：智能分配API Key使用，避免单个Key过载

## 使用指南

### 1. 启动改写任务

改写任务现在会自动启用失败恢复机制：

```typescript
// 在 rewriteChapters 函数中
const results = await rewriteChapters(
  chaptersData,
  rules,
  onProgress,
  onChapterComplete,
  concurrency,
  model,
  true // 启用失败恢复机制
);
```

### 2. 处理失败章节

#### 查看失败章节
- 任务完成后，失败的章节会在界面中高亮显示
- 点击章节可以查看详细的错误信息
- 支持展开查看技术细节和错误堆栈

#### 重试失败章节
1. 在失败章节列表中选择要重试的章节
2. 点击"重试选中章节"按钮
3. 系统会使用更保守的策略重新处理这些章节
4. 重试过程中会显示实时进度

#### 批量重试
- 使用"全选"功能可以一次性重试所有失败章节
- 重试会串行处理，避免API限流
- 每个章节之间有3秒间隔

### 3. 使用诊断工具

#### 打开诊断面板
- 在任务完成或失败后，点击"诊断"按钮
- 诊断面板会显示详细的分析报告

#### 诊断信息包含
1. **任务概览**：成功/失败章节统计
2. **API Key状态**：各个Key的使用情况和可用性
3. **错误分析**：错误类型分布和模式识别
4. **改进建议**：基于分析结果的具体建议

### 4. 最佳实践

#### 预防措施
1. **合理设置并发数**：建议设置为1-3，避免触发限流
2. **分批处理**：对于大量章节，建议分批处理
3. **错峰使用**：在API使用量较低的时间段进行处理
4. **监控配额**：定期检查API Key的使用配额

#### 故障处理
1. **查看诊断报告**：首先使用诊断工具了解问题原因
2. **调整策略**：根据诊断建议调整并发数或处理策略
3. **重试失败章节**：使用重试功能处理失败的章节
4. **联系支持**：如果问题持续存在，联系技术支持

## API 端点

### 重试失败章节
```
POST /api/rewrite/retry
{
  "jobId": "任务ID",
  "chapterNumbers": [1, 5, 10],
  "rules": "改写规则",
  "model": "gemini-2.5-flash-lite"
}
```

### 获取诊断信息
```
GET /api/rewrite/diagnostics?jobId=任务ID
```

## 技术细节

### 错误分类
- **API限流 (429)**：触发冷却机制，自动切换API Key
- **响应格式错误**：增强响应验证，记录详细错误信息
- **网络超时**：设置合理超时时间，支持重试
- **内容过滤**：识别安全过滤器拦截，提供调整建议

### 重试策略
- **指数退避**：2^n * 基础时间间隔
- **最大等待时间**：30秒上限，避免过长等待
- **API Key轮换**：失败后自动切换到其他可用Key
- **冷却期管理**：智能管理API Key的冷却时间

### 数据持久化
- **失败章节记录**：保存失败章节的原始内容和错误信息
- **重试历史**：记录所有重试尝试和结果
- **诊断数据**：保存诊断分析结果，便于后续查看

## 监控和告警

### 实时监控
- API Key使用率监控
- 错误率趋势分析
- 性能指标跟踪

### 告警机制
- 高失败率告警
- API配额不足告警
- 连接异常告警

这些改进确保了改写过程的稳定性和可靠性，最大程度地减少了章节内容丢失的风险。
